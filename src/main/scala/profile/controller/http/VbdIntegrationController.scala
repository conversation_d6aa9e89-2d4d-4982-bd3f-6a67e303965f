package profile.controller.http

import com.twitter.finagle.http.Request
import com.twitter.finatra.http.Controller
import com.twitter.inject.Logging
import profile.controller.http.filter.UserQuotaJwtReq
import profile.controller.http.filter.user.UserSignedInFilter
import profile.domain.response.BaseResponse
import profile.service.{HttpQuotaService, ProfileService, VBDDocumentInfo, VbdEyePassIntegrationService}
import profile.util.Constant
import vn.vhm.common.domain.profiling.Profiler

import javax.inject.Inject
import scala.concurrent.duration.DurationInt

/**
 * <AUTHOR> 10/18/24 10:39
 */
class VbdIntegrationController @Inject()(
                                          profileService: ProfileService,
                                          quotaService: HttpQuotaService,
                                          vbdService: VbdEyePassIntegrationService
                                        ) extends Controller with Logging {

  filter[UserSignedInFilter]
    .post("/user/vbd-sdk/token") {
      req: VbdDataRequest => {
        Profiler("HTTP-POST /user/vbd-sdk/token") {
          quotaService.calcQuotaUserMac3(
            req.request, req, mac = "user-request-vbd-token-by-day", ttlMac = 1.days, numCheckMac = 20, deleteIfSuccess = false,
            checkQuotaIP = Some(true), ttlIp = Some(1.days), numCheckIp = Some(500)
          ) {
            quotaService.calcQuotaUserMac(req, mac = "user-request-vbd-token-by-minute", ttl = 1.minutes, numCheck = 5, deleteIfSuccess = false) {
              for {
                _ <- profileService.canVerifyIdentityDocument(req.username)
                result <- vbdService.requestAccessToken(req.username)
              } yield result
            }
          }
        }
      }
    }

  filter[UserSignedInFilter]
    .post("/user/vbd-sdk/verify-document") {
      req: VbdDataRequest => {
        Profiler("HTTP-POST /user/vbd-sdk/verify-document") {
          quotaService.calcQuotaUserMac3(
            req.request, req, mac = "user-verify-vdb-document-by-day", ttlMac = 1.days, numCheckMac = 20, deleteIfSuccess = false,
            checkQuotaIP = Some(true), ttlIp = Some(1.days), numCheckIp = Some(500)
          ) {
            quotaService.calcQuotaUserMac(req, mac = "user-verify-vdb-document-by-minute", ttl = 1.minutes, numCheck = 5, deleteIfSuccess = false) {
              for {
                _ <- profileService.canVerifyIdentityDocument(req.username)
                doc <- vbdService.verifyDocument(req.username, req.vbdToken, req.documentInfo, req.requestIds, Some(req.lang))
                customerIdentity <- profileService.verifyIdentityByProvider(req.username, doc, sendAutoApproval = true)
              } yield BaseResponse(code = 0, data = Some(customerIdentity))
            }
          }
        }
      }
    }
}

case class VbdDataRequest(
                           documentInfo: Option[VBDDocumentInfo],
                           requestIds: Option[Map[String, String]],
                           vbdToken: Option[String],
                           tokenCaptcha: Option[String],
                           @Inject request: Request
                          ) extends UserQuotaJwtReq {

  import profile.controller.http.filter.user.UserContext.UserContextSyntax

  def username: String = request.user.username.get

  def lang: String = request.headerMap.getOrElse("Accept-Language", Constant.DEFAULT_LANGUAGE) match {
    case "vi-VN" => "vi"
    case "en-US" => "en"
    case x => x
  }

  override def quotaUsername(): String = username

  override def optTokenCaptcha(): Option[String] = tokenCaptcha
}