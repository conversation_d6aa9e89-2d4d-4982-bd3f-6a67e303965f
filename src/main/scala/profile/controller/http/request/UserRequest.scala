package profile.controller.http.request

import com.twitter.finagle.http.Request
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import profile.controller.http.filter.phone.PhoneQuotaJwtReq
import profile.controller.http.filter.user.UserContext.UserContextSyntax
import profile.controller.http.filter.{EmailQuotaJwtReq, UserQuotaJwtReq}
import profile.util.{CustomUtils, PasswordHelper, PhoneUtils}
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny

import javax.inject.Inject

/**
 * <AUTHOR> 2/27/24 12:06
 */
case class ChangePasswordRequest(
                                  oldPassword: Option[String],
                                  newPassword: String,
                                  @Inject request: Request
                                ) {

  @MethodValidation
  def checkValid: ValidationResult = try {
    if (oldPassword.contains(newPassword)) throw new Exception("New password must be different from old password")

    PasswordHelper.validatePassword(newPassword)

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }

  def oldPasswordHashed: Option[String] = oldPassword.map(PasswordHelper.hashPasswordBCrypt(request.user.username.get, _))

  def newPasswordHashed: String = PasswordHelper.hashPasswordBCrypt(request.user.username.get, newPassword)

}

case class DeactivateRequest(password: String,
                             @Inject request: Request) {

  @MethodValidation
  def checkValid: ValidationResult = try {

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }

  def passwordHashed: String = PasswordHelper.hashPasswordBCrypt(request.user.username.get, password)

  def username: String = request.user.username.get
}

case class SendCodeToVerifyEmailReq(
                                     tokenCaptcha: Option[String],
                                     @Inject request: Request
                                   ) extends UserQuotaJwtReq {

  override def quotaUsername(): String = request.user.username.get

  override def optTokenCaptcha(): Option[String] = tokenCaptcha
}

case class VerifyEmailReq(
                           email: String,
                           code: String,
                           tokenCaptcha: Option[String],
                           @Inject request: Request) extends EmailQuotaJwtReq {

  override def optQuotaEmail(): Option[String] = email.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

}

case class SendCodeToUpdateNewEmailReq(
                                        email: String,
                                        tokenCaptcha: Option[String],
                                        @Inject request: Request) extends EmailQuotaJwtReq {

  override def optQuotaEmail(): Option[String] = email.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

}

case class SendCodeToUpdatePhoneReq(phone: String,
                                    tokenCaptcha: Option[String],
                                    @Inject request: Request) extends PhoneQuotaJwtReq {

  val normPhone: String = PhoneUtils.normalizePhone(phone)

  override def optNormPhone(): Option[String] = normPhone.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

  def getPlatform: Option[String] = request.headerMap.get("platform")
}

case class UpdateStaffInfoReq(
                               vgEmployeeId: String,
                               tokenCaptcha: Option[String],
                               @Inject request: Request
                             )

case class UpdateNewEmailReq(
                              email: String,
                              code: String,
                              tokenCaptcha: Option[String],
                              @Inject request: Request
                            ) extends EmailQuotaJwtReq {

  @MethodValidation
  def checkValid: ValidationResult = try {

    if (!CustomUtils.isEmail(email)) throw new Exception("Invalid email")

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }

  override def optQuotaEmail(): Option[String] = email.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

}

case class UpdateNewPhoneReq(
                              phone: String,
                              code: String,
                              tokenCaptcha: Option[String],
                              @Inject request: Request
                            ) extends PhoneQuotaJwtReq {

  val normPhone: String = PhoneUtils.normalizePhone(phone)

  override def optNormPhone(): Option[String] = normPhone.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

}
