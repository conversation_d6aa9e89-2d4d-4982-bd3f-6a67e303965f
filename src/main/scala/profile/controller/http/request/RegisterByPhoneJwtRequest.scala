package profile.controller.http.request

import com.fasterxml.jackson.annotation.JsonIgnore
import com.twitter.finagle.http.Request
import com.twitter.finatra.validation.constraints.NotEmpty
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import profile.controller.http.filter.EmailQuotaJwtReq
import profile.controller.http.filter.phone.PhoneQuotaJwtReq
import profile.domain.request.{BaseIdentifyRequest, BaseShieldRequest}
import profile.domain.request.UpdateUserRequest.{checkTimeFormat, dayOfBirthFormat, enumGenders}
import profile.util.{PasswordHelper, PhoneUtils}
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny

import javax.inject.Inject
import scala.concurrent.duration.Duration

/**
 * <AUTHOR> 8/24/20 9:46 AM
 */

case class RegisterJwtRequest(
                               identify: String,
                               deviceId: String = "",
                               code: Option[String],
                               tokenCaptcha: Option[String],
                               @Inject request: Request
                             ) extends BaseIdentifyRequest with PhoneQuotaJwtReq with EmailQuotaJwtReq {

  override def optNormPhone(): Option[String] = if (isPhone) getNormPhone.toSome else None

  override def optQuotaEmail(): Option[String] = if (isEmail) getEmail.toSome else None

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

  override def optLockInDuration(): Option[Duration] = None

  def getPlatform: Option[String] = request.headerMap.get("platform")
}

case class CalcQuotaPhoneRequest(
                                  phone: String,

                                  tokenCaptcha: Option[String] = None,
                                  lockInDuration: Option[Duration] = None
                                ) extends PhoneQuotaJwtReq {

  val normPhone: String = PhoneUtils.normalizePhone(phone)

  override def optNormPhone(): Option[String] = normPhone.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

  override def optLockInDuration(): Option[Duration] = lockInDuration
}

case class CalcQuotaEmailRequest(
                                  email: String,

                                  tokenCaptcha: Option[String] = None,
                                  lockInDuration: Option[Duration] = None
                                ) extends EmailQuotaJwtReq {


  override def optQuotaEmail(): Option[String] = email.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

  override def optLockInDuration(): Option[Duration] = lockInDuration
}

case class RegisterByPhoneJwtRequest(
                                      phone: String,
                                      tokenCaptcha: Option[String],
                                      @Inject request: Request
                                    ) extends PhoneQuotaJwtReq {
  val normPhone: String = PhoneUtils.normalizePhone(phone)

  override def optNormPhone(): Option[String] = normPhone.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha
}

case class VerifyRegisterByPhoneJwtRequest(
                                            phone: String,
                                            code: String,
                                            tokenCaptcha: Option[String],
                                          ) extends PhoneQuotaJwtReq {
  val normPhone: String = PhoneUtils.normalizePhone(phone)

  override def optNormPhone(): Option[String] = normPhone.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha
}

case class VerifyRegisterJwtRequest(
                                     identify: String,
                                     deviceId: String = "",
                                     code: String,
                                     tokenCaptcha: Option[String],
                                     @Inject request: Request
                                   ) extends BaseIdentifyRequest with PhoneQuotaJwtReq with EmailQuotaJwtReq with BaseShieldRequest {

  override def optNormPhone(): Option[String] = if (isPhone) getNormPhone.toSome else None

  override def optQuotaEmail(): Option[String] = if (isEmail) getEmail.toSome else None

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

  override def optLockInDuration(): Option[Duration] = None
}
//
//case class ConfirmRegisterByJwtRequest(
//                                        phone: String,
//                                        tokenPhone: String,
//                                        deviceId: Option[String],
//                                        firstName: Option[String],
//                                        lastName: Option[String],
//                                        fullName: Option[String],
//                                        password: String,
//                                        referralCode: Option[String] = None
//                                      ) {
//  @MethodValidation
//  def checkValid: ValidationResult = try {
//    PasswordHelper.validatePassword(password)
//
//    ValidationResult.Valid()
//  } catch {
//    case e: Exception => ValidationResult.Invalid(e.getMessage)
//  }
//
//  val normPhone: String = PhoneUtils.normalizePhone(phone)
//
//  def registerData: RegisterUserJwtData = RegisterUserJwtData(
//    firstName = firstName,
//    lastName = lastName,
//    fullName = fullName,
//    password = password,
//    deviceId = deviceId,
//    referralCode = referralCode
//  )
//}

case class ConfirmRegisterJwtRequest(
                                      identify: String,
                                      token: String,
                                      deviceId: String,
                                      firstName: Option[String],
                                      lastName: Option[String],
                                      fullName: Option[String],
                                      password: String,
                                      gender: Option[String] = None,
                                      birthday: Option[String] = None,
                                      referralCode: Option[String] = None,
                                      @Inject request: Request
                                    ) extends BaseIdentifyRequest with BaseShieldRequest {
  @MethodValidation
  def checkValid: ValidationResult = try {
    PasswordHelper.validatePassword(password)

    if (gender.isDefined && !enumGenders.contains(gender.get)) throw new Exception("gender is invalid")
    if (birthday.isDefined && !checkTimeFormat(birthday.get, dayOfBirthFormat)) throw new Exception(s"`birthday` is invalid for format `$dayOfBirthFormat`")

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }

  def registerData: RegisterUserJwtData = RegisterUserJwtData(
    firstName = firstName,
    lastName = lastName,
    fullName = fullName,
    password = password,
    deviceId = deviceId,
    gender = gender,
    birthday = birthday,
    referralCode = referralCode
  )
}

case class RegisterUserOnboardJwtRequest(
                                          userId: String,
                                          password: String,
                                          token: String,
                                          deviceId: String,
                                          firstName: Option[String],
                                          lastName: Option[String],
                                          fullName: Option[String],
                                          gender: Option[String] = None,
                                          birthday: Option[String] = None,
                                          @Inject request: Request
                                        ) extends BaseShieldRequest {
  @MethodValidation
  def checkValid: ValidationResult = try {
    PasswordHelper.validatePassword(password)

    if (gender.isDefined && !enumGenders.contains(gender.get)) throw new Exception("gender is invalid")
    if (birthday.isDefined && !checkTimeFormat(birthday.get, dayOfBirthFormat)) throw new Exception(s"`birthday` is invalid for format `$dayOfBirthFormat`")

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }

  def registerData: RegisterUserJwtData = {
    RegisterUserJwtData(
      firstName = firstName,
      lastName = lastName,
      fullName = fullName,
      password = password,
      deviceId = deviceId,
      gender = gender,
      birthday = birthday
    )
  }
}

case class RegisterUserJwtData(
                                firstName: Option[String],
                                lastName: Option[String],
                                fullName: Option[String],
                                password: String,
                                deviceId: String,
                                gender: Option[String] = None,
                                birthday: Option[String] = None,
                                referralCode: Option[String] = None,
                              ) {

  @JsonIgnore
  def nameIsDefined: Boolean = firstName.isDefined || lastName.isDefined || fullName.isDefined

}

case class RefreshTokenRequest(@NotEmpty refreshToken: String, @Inject request: Request)