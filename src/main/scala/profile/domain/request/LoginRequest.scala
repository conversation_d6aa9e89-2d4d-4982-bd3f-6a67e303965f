package profile.domain.request

import com.fasterxml.jackson.annotation.JsonProperty
import com.twitter.finagle.http.Request
import profile.controller.http.filter.EmailQuotaJwtReq
import profile.controller.http.filter.phone.PhoneQuotaJwtReq
import profile.exception.{InvalidEmailException, UnSupportEmailException}
import profile.util.{CustomUtils, EmailHelper, EmailValidated, PhoneUtils}
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny

import javax.inject.Inject
import scala.concurrent.duration.Duration

/**
 * <AUTHOR>
 */
abstract class BaseIdentifyRequest {

  val identify: String

  def isEmail: Boolean = identify.contains("@")

  def isPhone: Boolean = vn.vhm.common.util.PhoneUtils.isPhoneNumber(identify)

  def getNormPhone: String = PhoneUtils.normalizePhone(identify)

  def getEmail: String = {
    getValidatedEmail.email
  }

  def getNormEmail: String = {
    getValidatedEmail.normalizedEmail
  }

  def getValidatedEmail: EmailValidated = {
    val emailData = EmailHelper.isValidEmail(identify)
    if (!emailData.isValid) {
      emailData.reason match {
        case Some("invalid_email_format") => throw InvalidEmailException()
        case _ => throw UnSupportEmailException(email = emailData.email, category = emailData.reason.get)
      }
    }
    emailData
  }

}

case class LoginBodyRequest(
                             identify: String,
                             password: String,
                             deviceId: Option[String] = None,

                             @JsonProperty(value = "login_type") loginType: Option[String],
                             code: Option[String],
                             tokenCaptcha: Option[String],
                             @Inject request: Request
                           ) extends BaseIdentifyRequest with PhoneQuotaJwtReq with EmailQuotaJwtReq {

  override def optNormPhone(): Option[String] = if (isPhone) getNormPhone.toSome else None

  override def optQuotaEmail(): Option[String] = if (isEmail) getEmail.toSome else None

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

  override def optLockInDuration(): Option[Duration] = None

  def getPlatform: Option[String] = request.headerMap.get("platform")
}

case class VerifyLoginPhoneRequest(
                                    phone: String,
                                    code: String,
                                    deviceId: String,

                                    tokenCaptcha: Option[String],
                                    @Inject request: Request
                                  ) extends PhoneQuotaJwtReq {
  val normPhone: String = PhoneUtils.normalizePhone(phone)

  override def optNormPhone(): Option[String] = normPhone.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha
}

//case class LoginByUserPassRequest(username: String, password: String, remember: Boolean) {
//  def passwordHashed: String = PasswordHelper.hashPassword(username, password)
//}

case class LoginByPhoneRequest(var normPhone: String,
                               password: String,

                               deviceId: String,
                               code: Option[String],
                               platform: Option[String],
                               tokenCaptcha: Option[String]
                              ) extends PhoneQuotaJwtReq {

  normPhone = PhoneUtils.normalizePhone(normPhone)

  override def optNormPhone(): Option[String] = normPhone.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha
}

case class LoginByEmailRequest(email: String,
                               password: String,

                               deviceId: String,
                               code: Option[String],
                               tokenCaptcha: Option[String]
                              ) extends EmailQuotaJwtReq {

  override def optQuotaEmail(): Option[String] = email.toSome

  override def optTokenCaptcha(): Option[String] = tokenCaptcha
}