package profile.domain.request

import com.twitter.finagle.http.Request
import profile.util.{Constant, CustomUtils}

/**
 * <AUTHOR>
 */
trait BaseShieldRequest {

  val request: Request

  private lazy val appBuildNumber: Option[String] = request.headerMap.get(Constant.APP_BUILD_NUMBER_HEADER)

  private lazy val tokenHeaderKey: Option[String] = Constant.SHIELD_PROTECT_BY_TOKEN_HEADERS.find(header => request.headerMap.get(header).exists(_.nonEmpty))

  private lazy val token: Option[String] = tokenHeaderKey.flatMap(request.headerMap.get)

  def getShieldDataRequest: ShieldDataRequest = {
    ShieldDataRequest(
      appVersion = appBuildNumber,
      tokenSource = tokenHeaderKey,
      token = token,
      lang = CustomUtils.getLang(request)
    )
  }

}

case class ShieldDataRequest(
                              appVersion: Option[String],
                              tokenSource: Option[String],
                              token: Option[String],
                              lang: String
                            )