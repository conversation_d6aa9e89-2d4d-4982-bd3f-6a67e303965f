package profile.service

import com.fasterxml.jackson.annotation.JsonProperty
import com.twitter.inject.Logging
import com.twitter.util.Future
import com.typesafe.config.Config
import profile.domain.request.ShieldDataRequest
import profile.exception.ShieldProtectByTokenException
import profile.service.Action.Action
import scalaj.http.Http
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.UnsupportedException
import vn.vhm.common.util.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sonHelperCamelCase}

/**
 * <AUTHOR>
 */
trait ShieldService {

  def checkAppVersionSupported(shieldProtectData: ShieldDataRequest): Future[Unit]

  def protectRequest[T](shieldProtectData: ShieldDataRequest,
                        action: Action.Action,
                        key: String,
                        metadata: Map[String, Object] = Map.empty[String, Object])(fn: => Future[T]): Future[T]
}

case class ShieldServiceImpl(shieldConfig: Config) extends ShieldService with Logging {

  private val clazz = getClass.getCanonicalName

  import vn.vhm.common.util.ZConfig.ImplicitConfig

  private val baseUrl = shieldConfig.getString("base_url")
  private val connectTimeout = shieldConfig.getInt("connect_timeout_ms", 30000)
  private val requestTimeout = shieldConfig.getInt("request_timeout_ms", 30000)
  private val shieldIsEnable = shieldConfig.getBoolean("enable", false)
  private val noAppCheckLatestBuildVersion = shieldConfig.getLong("no_app_check_latest_build_number", 0)
  private val protectRequestByTokenUrl = baseUrl + "/internal/protect-request-by-token"

  private val messageForOldApp = Map(
    "vi" -> "Vui lòng cập nhật ứng dụng để tiếp tục đăng ký và sử dụng các tính năng mới.",
    "en" -> "Please update the app to continue registration and access new features."
  )

  override def checkAppVersionSupported(shieldProtectData: ShieldDataRequest): Future[Unit] = Profiler(s"$clazz.checkAppBuildNumberSupported") {
    if (shieldIsEnable) {
      // check for AppCheck support
      shieldProtectData.appVersion match {
        case Some(build) if build.toLong <= noAppCheckLatestBuildVersion =>
          warn(s"Old app version not support => Denied\trequest=${JsonHelper.toJson(shieldProtectData)}")
          Future.exception(UnsupportedException(messageForOldApp(shieldProtectData.lang)))
        case None =>
          warn(s"Missing app version => Denied\trequest=${JsonHelper.toJson(shieldProtectData)}")
          Future.exception(UnsupportedException(messageForOldApp(shieldProtectData.lang)))
        case _ => Future.Unit
      }
    } else Future.Unit
  }

  override def protectRequest[T](req: ShieldDataRequest,
                                 action: Action.Action,
                                 key: String,
                                 metadata: Map[String, Object] = Map.empty[String, Object])(fn: => Future[T]): Future[T] = Profiler(s"$clazz.protectRequest") {
    for {
      // Check build number support shield
      _ <- checkAppVersionSupported(req)

      _ <- if (shieldIsEnable) {
        for {
          token <- async {
            req.token.getOrElse {
              warn(s"Client not support protect request by token => Denied\trequest=${JsonHelper.toJson(req)}, action=$action, key=$key, metadata=${JsonHelper.toJson(metadata)}")
              throw ShieldProtectByTokenException("Client not support")
            }
          }
          (isValid, msg) <- _verifyToken(req.tokenSource.getOrElse(""), token, action, key, metadata)
          result <- if (isValid) Future.Unit else async {
            warn(s"Protect request by token failed => Denied\trequest=${JsonHelper.toJson(req)}, action=$action, key=$key, metadata=${JsonHelper.toJson(metadata)}, reason=$msg")
            throw ShieldProtectByTokenException(msg)
          }
        } yield result

      } else async {
//        warn(s"Protect request by token is disabled => Bypassing verification for action ($action) and key ($key)")
      }

      result <- fn

    } yield result
  }

  /**
   * Verify the protect token for the given action and key
   *
   * @param tokenSource The header key for the token
   * @param token       The protect token to verify
   * @param action      The action being performed
   * @param key         The key for verification
   * @param metadata    Additional metadata for verification
   * @return Future[Boolean] indicating if the token is valid
   */
  private def _verifyToken(tokenSource: String,
                           token: String,
                           action: Action,
                           key: String,
                           metadata: Map[String, Object] = Map.empty[String, Object]): Future[(Boolean, String)] = Profiler("_verifyToken") {
    async {
      val payload = ProtectByTokenRequest(
        protectKey = key,
        protectAction = action.toString,
        tokenSource = tokenSource,
        token = token,
        metadata = Map.empty[String, Object]
      )

      try {
        val resp = Http(protectRequestByTokenUrl)
          .timeout(connTimeoutMs = connectTimeout, readTimeoutMs = requestTimeout)
          .header("Content-Type", "application/json")
          .postData(JsonHelperCamelCase.toJson(payload))
          .asString

        resp.code match {
          case 200 =>
            val result = JsonHelperCamelCase.fromJson[VClubShieldResp[ProtectByTokenResponse]](resp.body)
            if (!result.data.valid) {
              error(s"Failed to verify token ($token) for action ($action) and key ($key) => ${result.data.failureReason}")
            }
            (result.data.valid, result.data.failureReason)
          case 400 =>
            val result = JsonHelperCamelCase.fromJson[VClubShieldResp[ProtectByTokenResponse]](resp.body)
            error(s"Failed to verify token ($token) for action ($action) and key ($key) => ${resp.body}")
            (false, result.message.mkString(","))
          case _ =>
            // If status code error, we will by pass request
            error(s"Failed to call shield service (status code: ${resp.code}) => Bypass verify token ($token) for action ($action) and key ($key)")
            (true, "")
        }
      } catch {
        case e: Exception =>
          error(s"Exception when verify token (${e.getMessage}) => Bypass verify token ($token) for action ($action) and key ($key)", e)
          (true, "")
      }
    }
  }
}

case class VClubShieldResp[T](code: Int, message: Seq[String], data: T)

case class ProtectByTokenResponse(valid: Boolean,
                                  @JsonProperty("failureReason")
                                  failureReason: String
                                 )

case class ProtectByTokenRequest(
                                  protectKey: String,
                                  protectAction: String,
                                  tokenSource: String,
                                  token: String,
                                  metadata: Map[String, Object]
                                )


object Action extends Enumeration {
  type Action = Value

  val REGISTER_CHECK = Value("REGISTER_CHECK")
  val REGISTER_VERIFY = Value("REGISTER_VERIFY")
  val REGISTER_CONFIRM = Value("REGISTER_CONFIRM")
  val REGISTER_ONBOARDING = Value("REGISTER_ONBOARDING")

  val LOGIN = Value("LOGIN")

  val RESET_PASSWORD_CHECK = Value("RESET_PASSWORD_CHECK")
  val RESET_PASSWORD_VERIFY = Value("RESET_PASSWORD_VERIFY")
  val RESET_PASSWORD_CONFIRM = Value("RESET_PASSWORD_CONFIRM")

  val VBD_SDK_REQUEST_TOKEN = Value("VBD_SDK_REQUEST_TOKEN")
}