package profile.repository

import com.twitter.finatra.utils.FuturePools
import com.twitter.inject.Logging
import com.twitter.util.{Future, FuturePool}
import com.typesafe.config.Config
import org.apache.commons.io.FileUtils
import profile.repository.FileInternalDefine.{InternalPath, RawStoragePath}
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model._
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.{GetObjectPresignRequest, PutObjectPresignRequest}
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.{NotFoundException, RCustomException}

import java.io.{File, InputStream}
import java.time.Duration
import scala.collection.JavaConverters._
import scala.concurrent.duration.DurationInt

/**
 * <AUTHOR> 8/24/22 09:41
 */
case class S3SpecificFileRepository(region: String, bucket: String, basePath: String) extends Logging {

  protected val clazz = getClass.getCanonicalName

  private val s3Client = S3Client.builder().region(Region.of(region)).build()

  private val s3PreSigner = S3Presigner.builder()
    .region(Region.of(region))
    .build()
  private val preSignUrlS3Pool: FuturePool = FuturePools.fixedPool("vclub-unbounded-pool", 4)
  private val preSignUrlUploadTimeoutInMs = 30.minute.toMillis
  private val preSignUrlDownloadTimeoutInMs = 5.minute.toMillis

  def close(): Unit = {
    s3Client.close()
  }

  /**
   * Với s3 chỉ hỗ trợ file path, ko trả về full path (từ bucket)
   */
  def getInternalPath(filePath: String): InternalPath = {
    if (filePath.startsWith("/")) filePath.substring(1) else filePath
  }

  private def buildS3FileUrl(filePath: String): String = {
    if (filePath.isEmpty) throw RCustomException("file_repo_internal_error", "Unsupported empty file_path")
    Seq(
      if (basePath.startsWith("/")) basePath.substring(1) else basePath,
      if (filePath.startsWith("/")) filePath.substring(1) else filePath,
    ).filter(_.nonEmpty).mkString("/")
  }

  def put(localFilePath: String, contentType: String, filePath: String, metadata: Map[String, String], tags: Map[String, String]): RawStoragePath = {
    val s3FilePath = buildS3FileUrl(filePath)

    var putObjReq = PutObjectRequest.builder()
      .bucket(bucket)
      .key(s3FilePath)

    if (contentType.nonEmpty) putObjReq = putObjReq.contentType(contentType)
    if (metadata.nonEmpty) {
      putObjReq = putObjReq.metadata(metadata.asJava)
    }

    if (tags.nonEmpty) {
      putObjReq = putObjReq.tagging(
        Tagging.builder()
          .tagSet(tags.map(tuple => Tag.builder().key(tuple._1).value(tuple._2).build()).toSeq: _*)
          .build()
      )
    }

    val localFile = new java.io.File(localFilePath)

    s3Client
      .putObject(putObjReq.build(), RequestBody.fromFile(localFile))
      .eTag()

    FileUtils.deleteQuietly(localFile)

    s3FilePath

  }

}

class S3FileRepository(s3Conf: Config) extends FileRepository with Logging {

  protected val clazz = getClass.getCanonicalName

  private val region = s3Conf.getString("region")
  private val bucket = s3Conf.getString("bucket")
  private val basePath = s3Conf.getString("base_path")

  private val temporaryBucket = s3Conf.getString("temporary_bucket")
  private val temporaryPrefixPath = s3Conf.getString("temporary_prefix_path")

  private val s3Client = S3Client.builder().region(Region.of(region)).build()

  private val s3PreSigner = S3Presigner.builder()
    .region(Region.of(region))
    .build()
  private val preSignUrlS3Pool: FuturePool = FuturePools.fixedPool("vclub-unbounded-pool", 4)
  private val preSignUrlUploadTimeoutInMs = 30.minute.toMillis
  private val preSignUrlDownloadTimeoutInMs = 5.minute.toMillis

  def close(): Unit = {
    s3Client.close()
  }

  /**
   * Với s3 chỉ hỗ trợ file path, ko trả về full path (từ bucket)
   */
  override def getInternalPath(filePath: String): InternalPath = {
    if (filePath.startsWith("/")) filePath.substring(1) else filePath
  }

  private def buildS3FileUrl(filePath: String): String = {
    if (filePath.isEmpty) throw RCustomException("file_repo_internal_error", "Unsupported empty file_path")
    Seq(
      if (basePath.startsWith("/")) basePath.substring(1) else basePath,
      if (filePath.startsWith("/")) filePath.substring(1) else filePath,
    ).filter(_.nonEmpty).mkString("/")
  }

  def put(filePath: String, is: InputStream, size: Long, contentType: String, metadata: Map[String, String], tags: Map[String, String]): InternalPath = Profiler(s"$clazz.put") {
    val s3FileUrl = buildS3FileUrl(filePath)

    var putObjReq = PutObjectRequest.builder()
      .bucket(bucket)
      .key(s3FileUrl)

    if (contentType.nonEmpty) putObjReq = putObjReq.contentType(contentType)
    if (metadata.nonEmpty) {
      putObjReq = putObjReq.metadata(metadata.asJava)
    }

    if (tags.nonEmpty) {
      putObjReq = putObjReq.tagging(
        Tagging.builder()
          .tagSet(tags.map(tuple => Tag.builder().key(tuple._1).value(tuple._2).build()).toSeq: _*)
          .build()
      )
    }

    s3Client
      .putObject(putObjReq.build(), RequestBody.fromContentProvider(() => is, size, contentType))
      .eTag()

    is.close()

    getInternalPath(filePath)
  }

  override def preparePut(localFilePath: InternalPath, contentType: InternalPath, filePath: InternalPath, metadata: Map[InternalPath, InternalPath], tags: Map[InternalPath, InternalPath]): InternalPath = {
    getInternalPath(filePath)
  }

  override def put(localFilePath: InternalPath, contentType: InternalPath, filePath: InternalPath, metadata: Map[InternalPath, InternalPath], tags: Map[InternalPath, InternalPath]): InternalPath = {
    val s3FilePath = buildS3FileUrl(filePath)

    var putObjReq = PutObjectRequest.builder()
      .bucket(bucket)
      .key(s3FilePath)

    if (contentType.nonEmpty) putObjReq = putObjReq.contentType(contentType)
    if (metadata.nonEmpty) {
      putObjReq = putObjReq.metadata(metadata.asJava)
    }

    if (tags.nonEmpty) {
      putObjReq = putObjReq.tagging(
        Tagging.builder()
          .tagSet(tags.map(tuple => Tag.builder().key(tuple._1).value(tuple._2).build()).toSeq: _*)
          .build()
      )
    }

    val localFile = new java.io.File(localFilePath)

    s3Client
      .putObject(putObjReq.build(), RequestBody.fromFile(localFile))
      .eTag()

    FileUtils.deleteQuietly(localFile)

    getInternalPath(filePath)
  }

  override def del(filePath: String): Unit = {
    val deleteObjReq = DeleteObjectRequest.builder()
      .bucket(bucket)
      .key(buildS3FileUrl(filePath))

    s3Client.deleteObject(deleteObjReq.build())
  }

  override def downloadPath(filePath: String): Future[FileResponse] = {
    async {
      try {
        val getObjReq = GetObjectRequest.builder()
          .bucket(bucket)
          .key(buildS3FileUrl(filePath))

        val resp = s3Client.getObjectAsBytes(getObjReq.build())
        FileResponse(
          is = resp.asInputStream(),
          contentType = resp.response().contentType()
        )
      } catch {
        case _: software.amazon.awssdk.services.s3.model.NoSuchKeyException => throw NotFoundException(filePath)
      }
    }
  }

  override def buildPreSignUrlForUpload(filePath: String, fileContentType: String,
                                        metadata: Map[String, String], tags: Map[String, String]): Future[PresignUploadData] = Profiler(s"$clazz.buildPreSignUrlForUpload") {
    preSignUrlS3Pool {
      val s3FilePath = buildS3FileUrl(filePath)

      val putObjectReq = PutObjectRequest.builder()
        .bucket(bucket)
        .key(s3FilePath)
        .contentType(fileContentType)
      //      putObjectReq.contentLength(size)

      if (metadata.nonEmpty) putObjectReq.metadata(metadata.asJava)
      if (tags.nonEmpty) {
        putObjectReq.tagging(
          Tagging.builder()
            .tagSet(tags.map(tuple => Tag.builder().key(tuple._1).value(tuple._2).build()).toSeq: _*)
            .build()
        )
      }

      val putObjectPreSignReq = PutObjectPresignRequest.builder()
        .signatureDuration(Duration.ofMillis(preSignUrlUploadTimeoutInMs))
        .putObjectRequest(putObjectReq.build())

      val presignUrl = s3PreSigner.presignPutObject(putObjectPreSignReq.build())
        .url().toString

      PresignUploadData(
        presignUrl = presignUrl,
        presignHeaders = metadata.map(pair => (s"x-amz-meta-${pair._1}", pair._2))
      )
    }
  }

  override def buildPreSignUrlForDownload(filePath: String): Future[PresignDownloadData] = Profiler(s"$clazz.buildPreSignUrlForDownload") {
    preSignUrlS3Pool {
      val s3FilePath = buildS3FileUrl(filePath)

      val getObjectReq = GetObjectRequest.builder()
        .bucket(bucket)
        .key(s3FilePath)

      val getObjectPreSignReq = GetObjectPresignRequest.builder()
        .signatureDuration(Duration.ofMillis(preSignUrlDownloadTimeoutInMs))
        .getObjectRequest(getObjectReq.build())

      val preSignUrl = s3PreSigner.presignGetObject(getObjectPreSignReq.build())
        .url().toString


      PresignDownloadData(presignUrl = preSignUrl)

    }
  }

  def listDirectFilesInFolder(externalFolderPath: String): Future[Seq[FileInfo]] = Profiler(s"$clazz.listFilesDirectInFolder") {
    async {
      val s3FolderPath = buildS3FileUrl(externalFolderPath)

      val response: ListObjectsV2Response = listFilesInFolder(s3FolderPath)

      response.contents().asScala.map { objectSummary =>
          val name = {
            objectSummary.key().lastIndexOf("/") match {
              case -1 => objectSummary.key()
              case i => objectSummary.key().substring(i + 1)
            }
          }
          val externalPath = objectSummary.key().replaceFirst(s3FolderPath, externalFolderPath)
          FileInfo(
            name = name,
            path = objectSummary.key(),
            externalPath = externalPath,
            size = objectSummary.size(),
            contentType = "", modTime = objectSummary.lastModified.toEpochMilli, metadata = Map()
          )
        }
        .filter(_.path != s3FolderPath)
    }
  }

  override def existFileOrFolder(fileOrFolderPath: InternalPath): Future[Boolean] = Profiler(s"$clazz.existFileOrFolder") {
    async {
      val s3FileOrFOlderPath = buildS3FileUrl(fileOrFolderPath)

      try {

        listFilesInFolder(s3FileOrFOlderPath)
          .contents().iterator().hasNext

        //        val headObjectRequest = HeadObjectRequest.builder()
        //          .bucket(bucket)
        //          .key(s3FileOrFOlderPath)
        //          .build()
        //
        //        s3Client.headObject(headObjectRequest)
        //
        //        true
      } catch {
        case e: S3Exception if e.statusCode() == 404 => false
        case e: Exception =>
          error(s"Failed when existFileOrFolder($fileOrFolderPath): ${e.getMessage}", e)
          false
      }
    }
  }

  private def listFilesInFolder(prefixPath: String) = {
    val listObjectReq = ListObjectsV2Request.builder()
      .bucket(bucket)
      .prefix(prefixPath)
      .delimiter("/")
      .build()
    s3Client.listObjectsV2(listObjectReq)
  }

  def listAllFilesInFolder(folderPath: String, holder: scala.collection.mutable.ListBuffer[FileInfo]): Unit = {
    val response: ListObjectsV2Response = listFilesInFolder(folderPath)

    response.contents().forEach { objectSummary =>

      val name = objectSummary.key()

      holder += FileInfo(
        name = name,
        path = objectSummary.key(),
        externalPath = "",
        size = objectSummary.size(),
        contentType = "", modTime = objectSummary.lastModified.toEpochMilli, metadata = Map()
      )
    }

    response.commonPrefixes().forEach { commonPrefix =>
      listAllFilesInFolder(commonPrefix.prefix(), holder)
    }
  }


  override def putPhotoTempFromFile(externalId: String, data: File, contentType: String): String = Profiler(s"$clazz.putPhotoTempFromFile") {
    val path = s"$temporaryPrefixPath/$externalId"

    val putObjReq = PutObjectRequest.builder()
      .bucket(temporaryBucket)
      .key(path)
      .contentType(contentType)

    s3Client
      .putObject(putObjReq.build(), RequestBody.fromFile(data))
      .eTag()

    s"s3://$temporaryBucket/$path"
  }

}

case class FileInfo(
                     name: String,
                     externalPath: String,
                     path: String,
                     size: Long,
                     contentType: String,
                     modTime: Long,
                     metadata: Map[String, Seq[String]],

                     //enhance data
                     externalUrl: Option[String] = None
                   )