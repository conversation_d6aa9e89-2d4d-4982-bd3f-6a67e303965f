package profile.util

import com.twitter.finatra.utils.FuturePools
import com.twitter.util.FuturePool
import vn.vhm.common.util.ZConfig

/**
 * <AUTHOR> 7/12/24 16:35
 */
object Constant {

  val listPnl = ZConfig.getStringSeq("main.list_pnl", Nil)

  val listCustomerScoreType = ZConfig.getStringSeq("main.list_customer_score_type", Nil)

  val listPnlMappingByIdentityDoc = ZConfig.getStringSeq("main.list_pnl_mapping_by_identity_doc", Nil)

  val futurePoolLock: FuturePool = FuturePools.unboundedPool("vhm-unbounded-pool")

  val JWT_CLAIM_EMAIL_F = "email"
  val JWT_CLAIM_EMAIL_VERIFIED_F = "email_verified"
  val JWT_CLAIM_PHONE_F = "phone"
  val JWT_CLAIM_PHONE_VERIFIED_F = "phone_verified"
  val JWT_CLAIM_FULL_NAME_F = "full_name"
  val JWT_CLAIM_CREATED_AT_F = "created_at"

  val JWT_CLAIM_REQUESTED_BY_F = "requested_by"
  val JWT_CLAIM_REQUIRED_BY_VALUE_INTERNAL = "internal"
  val JWT_CLAIM_REQUIRED_BY_VALUE_USER = "user"
  val JWT_CLAIM_REQUIRED_BY_VALUE_EMPTY = ""
  val JWT_CLAIM_PNL = "pnl"
  val JWT_CLAIM_PNL_VINCLUB = "VINCLUB"
  val JWT_CLAIM_DEVICE_ID = "dvid"

  val CACHE_LAST_LOGIN_SESSION = "customer-svc-customer-last-login-event:"

  val DEFAULT_LANGUAGE = "vi"

  val DEMOGRAPHIC_METADATA_PNL_TIER_POSTFIX = "_tier"
  val DEMOGRAPHIC_METADATA_PNL_SPENDING_AMOUNT_POSTFIX = "_spending_amount"
  val DEMOGRAPHIC_METADATA_CUSTOMER_SCORE_POSTFIX = "_customer_score"
  val DEMOGRAPHIC_METADATA_CUSTOMER_SCORE_RANK_POSTFIX = "_customer_score_rank"

  val USER_SELF = "SELF"
  val USER_SYSTEM = "SYSTEM"

  val APP_BUILD_NUMBER_HEADER = "build"
  val SHIELD_PROTECT_BY_TOKEN_HEADERS: Seq[String] = Seq("X-Firebase-AppCheck")

  val CACHE_CUSTOMER_OTP = "customersvc_CUSTOMER_OTP"
  val CACHE_CUSTOMER_QUOTA = "customersvc_CUSTOMER_QUOTA"
  val CACHE_CUSTOMER_LOCK = "customersvc_CUSTOMER_LOCK"
  val CACHE_INTEGRATION = "customersvc_INTEGRATION"

}
